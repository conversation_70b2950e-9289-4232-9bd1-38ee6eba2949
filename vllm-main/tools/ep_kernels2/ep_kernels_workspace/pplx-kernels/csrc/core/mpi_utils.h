#pragma once

#define MPICHECK(cmd)                                                                              \
  do {                                                                                             \
    int e = cmd;                                                                                   \
    if (e != MPI_SUCCESS) {                                                                        \
      printf("Failed: MPI error %s:%d '%d'\n", __FILE__, __LINE__, e);                             \
      exit(EXIT_FAILURE);                                                                          \
    }                                                                                              \
  } while (0)
