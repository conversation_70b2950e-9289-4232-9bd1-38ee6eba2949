# pyright: reportCallIssue=false

from collections.abc import Sequence

import torch

from .ops import _ops

###### NVSHMEM ######


def nvshmem_get_unique_id() -> torch.Tensor:
    return _ops.nvshmem_get_unique_id()


def nvshmem_unique_id_size() -> int:
    return _ops.nvshmem_unique_id_size()


def nvshmem_alloc_empty_unique_id() -> torch.Tensor:
    return torch.zeros(nvshmem_unique_id_size(), dtype=torch.uint8, device="cpu")


def nvshmem_init(uid: torch.Tensor, rank: int, world_size: int) -> int:
    status = _ops.nvshmem_init(uid, rank, world_size)
    torch.cuda.synchronize()
    return status


def nvshmem_alltoall(dest: torch.Tensor, source: torch.Tensor) -> None:
    return _ops.nvshmem_alltoall(dest, source)


def nvshmem_finalize() -> None:
    torch.cuda.synchronize()
    _ops.nvshmem_finalize()


def nvshmem_my_pe() -> int:
    return _ops.nvshmem_my_pe()


def nvshmem_n_pes() -> int:
    return _ops.nvshmem_n_pes()


def nvshmem_malloc(
    shape: Sequence[int],
    dtype: torch.dtype,
    device: torch.device,
) -> torch.Tensor:
    return _ops.nvshmem_malloc(shape, dtype, device)


def nvshmem_barrier_all() -> None:
    _ops.nvshmem_barrier_all()


def nvshmem_barrier_all_on_current_stream() -> None:
    _ops.nvshmem_barrier_all_on_current_stream()
