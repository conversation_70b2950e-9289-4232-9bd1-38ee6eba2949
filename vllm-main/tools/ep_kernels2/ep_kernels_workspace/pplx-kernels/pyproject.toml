[project]
name = "pplx-kernels"
version = "0.0.1"
description = "Perplexity CUDA Kernels"
readme = "README.md"
requires-python = ">=3.9"

[build-system]
requires = ["setuptools>=61.0", "wheel", "torch"]
build-backend = "setuptools.build_meta"

[tool.ruff]
line-length = 88

[tool.ruff.lint.isort]
combine-as-imports = true
known-first-party = ["tests", "pplx_kernels"]

[tool.ruff.lint]
select = [
    "E",    # pycodestyle
    "W",    # pycodestyle warnings
    "F",    # Pyflakes
    "UP",   # pyupgrade
    "I",    # isort
    "SIM",  # flake8-simplify
    "C4",   # flake8-comprehensions
    "PT",   # flake8-pytest
    "PIE",  # flake8-pie
    "EXE",  # flake8-executable
    "A",    # flake8-builtins
    "B",    # flake8-bugbear
    "ANN",  # flake8-annotations
    "BLE",  # flake8-blind-except
    "LOG",  # flake8-logging
    "G",    # flake8-logging-format
    "TCH",  # flake8-type-checking
    "RSE",  # flake8-raise
    "RET",  # flake8-return
    "T20",  # flake8-print
    "ICN",  # flake8-import-conventions
    "TID",  # flake8-tidy-imports
    "INP",  # flake8-no-pep420
    "NPY",  # numpy
    "FURB", # refurb
    "TRY",  # tryceratops
    "FLY",  # flynt,
]
ignore = [
    "E501",   # Line too long
    "ANN401", # Allow Any
    "TRY003", # Allow long messages
    "SIM117", # Allow nested with
]
